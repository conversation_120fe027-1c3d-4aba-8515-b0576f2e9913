package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
)

type SceneConfig struct {
	Scenes []struct {
		ID              int    `json:"id"`
		Description     string `json:"description"`
		DurationSeconds int    `json:"duration_seconds"`
		Style           string `json:"style"`
		Keywords        string `json:"keywords"`
		Prompt          string `json:"prompt"`
	} `json:"scenes"`
}

type AudioVideoMatcher struct {
	NovelID       string
	Chapter       int
	FreepikAPI    string
	AnimationType string
}

func NewAudioVideoMatcher(novelID string, chapter int, freepikAPI, animationType string) *AudioVideoMatcher {
	return &AudioVideoMatcher{
		NovelID:       novelID,
		Chapter:       chapter,
		FreepikAPI:    freepikAPI,
		AnimationType: animationType,
	}
}

func (avm *AudioVideoMatcher) GetAudioDuration(audioPath string) (float64, error) {
	cmd := exec.Command("ffprobe", "-v", "quiet", "-show_entries", "format=duration",
		"-of", "default=noprint_wrappers=1:nokey=1", audioPath)

	output, err := cmd.Output()
	if err != nil {
		return 0, err
	}

	duration, err := strconv.ParseFloat(strings.TrimSpace(string(output)), 64)
	if err != nil {
		return 0, err
	}

	return duration, nil
}

func (avm *AudioVideoMatcher) LoadSceneConfig() (*SceneConfig, error) {
	configPath := fmt.Sprintf("novels/%s/config/chapter_%d_scenes.json", avm.NovelID, avm.Chapter)

	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read scene config: %v", err)
	}

	var config SceneConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, fmt.Errorf("failed to parse scene config: %v", err)
	}

	return &config, nil
}

func (avm *AudioVideoMatcher) GenerateFreepikImages() error {
	fmt.Printf("🎨 Generating Freepik images for %s chapter %d...\n", avm.NovelID, avm.Chapter)

	cmd := exec.Command("go", "run", "cmd/novel-image-generator-freepik/main.go",
		avm.NovelID, strconv.Itoa(avm.Chapter), avm.FreepikAPI)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return cmd.Run()
}

func (avm *AudioVideoMatcher) GenerateMinimaxVideos() error {
	fmt.Printf("🎬 Generating Minimax AI videos for %s chapter %d...\n", avm.NovelID, avm.Chapter)

	cmd := exec.Command("go", "run", "cmd/freepik-minimax-pipeline/main.go",
		avm.NovelID, strconv.Itoa(avm.Chapter), avm.FreepikAPI, avm.AnimationType)

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return cmd.Run()
}

func (avm *AudioVideoMatcher) CreateAudioMatchedVideo(audioPath string) error {
	fmt.Printf("🎵 Creating audio-matched video for %s chapter %d...\n", avm.NovelID, avm.Chapter)

	// Get audio duration
	audioDuration, err := avm.GetAudioDuration(audioPath)
	if err != nil {
		return fmt.Errorf("failed to get audio duration: %v", err)
	}

	fmt.Printf("📊 Audio duration: %.1f seconds (%.1f minutes)\n", audioDuration, audioDuration/60)

	// Find generated video clips
	videoDir := fmt.Sprintf("novels/%s/output/videos/minimax_videos", avm.NovelID)
	videoPattern := fmt.Sprintf("chapter_%d_*.mp4", avm.Chapter)

	videoClips, err := filepath.Glob(filepath.Join(videoDir, videoPattern))
	if err != nil {
		return fmt.Errorf("failed to find video clips: %v", err)
	}

	if len(videoClips) == 0 {
		return fmt.Errorf("no video clips found in %s", videoDir)
	}

	fmt.Printf("🎬 Found %d video clips to combine\n", len(videoClips))

	// Output path
	outputPath := fmt.Sprintf("novels/%s/output/videos/chapter_%d_audio_matched_final.mp4",
		avm.NovelID, avm.Chapter)

	// Create Python script call
	pythonScript := `
import subprocess
import os

def create_audio_matched_video(audio_path, video_clips, output_path, target_duration):
	# Build FFmpeg command for seamless video looping
	inputs = ['-i', audio_path]
	
	# Add video inputs
	for clip in video_clips:
		inputs.extend(['-i', clip])
	
	# Create filter for concatenating and looping videos
	video_filters = []
	for i in range(1, len(video_clips) + 1):  # Start from 1 (audio is 0)
		video_filters.append(f'[{i}:v]')
	
	# Calculate loops needed
	clip_duration = 22  # Approximate total duration of our clips
	loops_needed = int(target_duration / clip_duration) + 2
	
	# Create loop sequence
	loop_filters = []
	for loop in range(loops_needed):
		loop_filters.extend(video_filters)
	
	concat_filter = ''.join(loop_filters) + f'concat=n={len(loop_filters)}:v=1:a=0[vout]'
	
	cmd = [
		'ffmpeg', '-y'] + inputs + [
		'-filter_complex', concat_filter,
		'-map', '[vout]',
		'-map', '0:a',
		'-c:v', 'libx264',
		'-c:a', 'aac',
		'-t', str(target_duration),
		'-r', '24',
		'-preset', 'medium',
		output_path
	]
	
	result = subprocess.run(cmd, capture_output=True, text=True)
	return result.returncode == 0

# Main execution
audio_path = "` + audioPath + `"
video_clips = ` + fmt.Sprintf("%q", videoClips) + `
output_path = "` + outputPath + `"
target_duration = ` + fmt.Sprintf("%.1f", audioDuration) + `

success = create_audio_matched_video(audio_path, video_clips, output_path, target_duration)

if success:
	print("✅ Audio-matched video created successfully!")
	print(f"📁 Output: {output_path}")
else:
	print("❌ Failed to create audio-matched video")
	exit(1)
`

	// Write and execute Python script
	scriptPath := "temp_audio_video_matcher.py"
	err = os.WriteFile(scriptPath, []byte(pythonScript), 0644)
	if err != nil {
		return fmt.Errorf("failed to write Python script: %v", err)
	}
	defer os.Remove(scriptPath)

	cmd := exec.Command("python3", scriptPath)
	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr

	return cmd.Run()
}

func (avm *AudioVideoMatcher) ProcessChapter() error {
	fmt.Printf("🚀 Starting Audio-Video Matching Pipeline\n")
	fmt.Printf("📖 Novel: %s, Chapter: %d\n", avm.NovelID, avm.Chapter)
	fmt.Printf("🎨 Freepik API: %s, Animation: %s\n", avm.FreepikAPI, avm.AnimationType)
	fmt.Println("=" + strings.Repeat("=", 50))

	// Step 1: Check if audio exists
	audioPath := fmt.Sprintf("novels/%s/output/audio/chapter_%d_thai_audio.wav", avm.NovelID, avm.Chapter)
	if _, err := os.Stat(audioPath); os.IsNotExist(err) {
		return fmt.Errorf("audio file not found: %s. Please generate audio first using step8-audio-standalone", audioPath)
	}

	fmt.Printf("✅ Audio file found: %s\n", audioPath)

	// Step 2: Generate Freepik images based on scenes
	fmt.Printf("\n🎨 Step 1: Generating Freepik Images\n")
	err := avm.GenerateFreepikImages()
	if err != nil {
		return fmt.Errorf("failed to generate Freepik images: %v", err)
	}

	// Step 3: Generate Minimax AI videos
	fmt.Printf("\n🎬 Step 2: Generating Minimax AI Videos\n")
	err = avm.GenerateMinimaxVideos()
	if err != nil {
		return fmt.Errorf("failed to generate Minimax videos: %v", err)
	}

	// Step 4: Create audio-matched final video
	fmt.Printf("\n🎵 Step 3: Creating Audio-Matched Final Video\n")
	err = avm.CreateAudioMatchedVideo(audioPath)
	if err != nil {
		return fmt.Errorf("failed to create audio-matched video: %v", err)
	}

	fmt.Printf("\n🎉 Audio-Video Matching Complete!\n")
	fmt.Printf("🎯 Created perfectly synchronized VRMMORPG audiobook video\n")
	fmt.Printf("📁 Output: novels/%s/output/videos/chapter_%d_audio_matched_final.mp4\n", avm.NovelID, avm.Chapter)

	return nil
}

func main() {
	// Check if running as web service (Railway sets PORT env var)
	if port := os.Getenv("PORT"); port != "" {
		startWebServer(port)
		return
	}

	// CLI mode
	if len(os.Args) < 5 {
		fmt.Println("Usage: go run cmd/audio-video-matcher/main.go <novel-id> <chapter> <freepik-api> <animation-type>")
		fmt.Println()
		fmt.Println("Example: go run cmd/audio-video-matcher/main.go reincarnation-of-the-strongest-sword-god 284 mystic wealth")
		fmt.Println()
		fmt.Println("Freepik APIs: mystic, flux_expand, reimagine, imagen4_ultra")
		fmt.Println("Animation Types: wealth, character, scene, audiobook")
		os.Exit(1)
	}

	novelID := os.Args[1]
	chapter, err := strconv.Atoi(os.Args[2])
	if err != nil {
		log.Fatalf("Invalid chapter number: %v", err)
	}

	freepikAPI := os.Args[3]
	animationType := os.Args[4]

	matcher := NewAudioVideoMatcher(novelID, chapter, freepikAPI, animationType)

	err = matcher.ProcessChapter()
	if err != nil {
		log.Fatalf("❌ Error: %v", err)
	}
}

func startWebServer(port string) {
	http.HandleFunc("/", handleRoot)
	http.HandleFunc("/health", handleHealth)
	http.HandleFunc("/process", handleProcess)

	fmt.Printf("🚀 Audio-Video Matcher API starting on port %s\n", port)
	log.Fatal(http.ListenAndServe(":"+port, nil))
}

func handleRoot(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	response := map[string]interface{}{
		"service": "Thai Audiobook AI - Audio Video Matcher",
		"status":  "running",
		"endpoints": map[string]string{
			"/health":  "Health check",
			"/process": "POST - Process audio-video matching",
		},
		"usage": map[string]interface{}{
			"method": "POST",
			"url":    "/process",
			"body": map[string]string{
				"novel_id":       "reincarnation-of-the-strongest-sword-god",
				"chapter":        "284",
				"freepik_api":    "mystic",
				"animation_type": "wealth",
			},
		},
	}
	json.NewEncoder(w).Encode(response)
}

func handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"status": "healthy"})
}

func handleProcess(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		NovelID       string `json:"novel_id"`
		Chapter       string `json:"chapter"`
		FreepikAPI    string `json:"freepik_api"`
		AnimationType string `json:"animation_type"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	chapter, err := strconv.Atoi(req.Chapter)
	if err != nil {
		http.Error(w, "Invalid chapter number", http.StatusBadRequest)
		return
	}

	matcher := NewAudioVideoMatcher(req.NovelID, chapter, req.FreepikAPI, req.AnimationType)

	// Process in background for long-running tasks
	go func() {
		err := matcher.ProcessChapter()
		if err != nil {
			log.Printf("Error processing chapter: %v", err)
		}
	}()

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status":   "processing",
		"message":  "Audio-video matching started",
		"novel_id": req.NovelID,
		"chapter":  req.Chapter,
	})
}
